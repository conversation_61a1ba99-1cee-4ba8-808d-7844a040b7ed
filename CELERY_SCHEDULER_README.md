# Taxi Userbot - Celery Scheduler Implementation

Bu loyihada Celery scheduler yordamida avtomatik xabar yuborish tizimi implement qilindi.

## Xususiyatlar

- ✅ Celery Worker va Beat Scheduler
- ✅ Django Celery Beat (Database Scheduler)
- ✅ Scheduled Message modeli
- ✅ API endpoints for CRUD operations
- ✅ Admin interface
- ✅ Error handling va retry logic
- ✅ Logging

## O'rnatish

1. **Dependencies o'rnatish:**
```bash
pip install -r requirements.txt
```

2. **Migration qilish:**
```bash
python manage.py migrate
```

3. **Celery services ishga tushirish:**
```bash
./start_celery.sh
```

4. **Django server ishga tushirish:**
```bash
python manage.py runserver 8000
```

## API Endpoints

### 1. Scheduled Message Yaratish
```bash
curl --location 'http://localhost:8000/bot/api/create-scheduled-message/' \
--header 'Content-Type: application/json' \
--data '{
    "user_id": 7608799593,
    "text": "Bu test xabari AUTO folderdagi barcha kanallarga yuborilmoqda.",
    "interval_minutes": 5
}'
```

**Response:**
```json
{
    "success": true,
    "message": "Scheduled message created successfully",
    "scheduled_message_id": 1,
    "task_id": "scheduled_message_1_7608799593",
    "interval_minutes": 5,
    "next_run_time": "2025-07-26T05:55:00Z"
}
```

### 2. Scheduled Message To'xtatish
```bash
curl --location 'http://localhost:8000/bot/api/stop-scheduled-message/' \
--header 'Content-Type: application/json' \
--data '{
    "scheduled_message_id": 1
}'
```

### 3. Scheduled Messages Ro'yxati
```bash
curl --location 'http://localhost:8000/bot/api/list-scheduled-messages/?user_id=7608799593&is_active=true'
```

### 4. Bir martalik Xabar Yuborish (mavjud API)
```bash
curl --location 'http://localhost:8000/bot/api/send-auto-message/' \
--header 'Content-Type: application/json' \
--data '{
    "user_id": 7608799593,
    "text": "Bu test xabari AUTO folderdagi barcha kanallarga yuborilmoqda."
}'
```

## Celery Services Boshqarish

### Ishga tushirish:
```bash
./start_celery.sh
```

### To'xtatish:
```bash
./stop_celery.sh
```

### Status tekshirish:
```bash
# Worker status
ps aux | grep "celery.*worker"

# Beat status  
ps aux | grep "celery.*beat"
```

### Log fayllarni ko'rish:
```bash
# Worker logs
tail -f celery_worker.log

# Beat logs
tail -f celery_beat.log
```

## Admin Interface

Django admin orqali scheduled messages ni boshqarish mumkin:
- http://localhost:8000/admin/userbot/scheduledmessage/

## Database Schema

### ScheduledMessage Model:
- `user_id`: Telegram user ID
- `text`: Yuborilishi kerak bo'lgan matn
- `interval_minutes`: Interval (daqiqalarda)
- `is_active`: Faol holat
- `status`: pending/sent/failed/cancelled
- `sent_count`: Yuborilgan xabarlar soni
- `last_sent_at`: Oxirgi yuborilgan vaqt
- `celery_task_id`: Celery task ID

## Qanday Ishlaydi

1. **Scheduled Message Yaratish:**
   - API orqali scheduled message yaratiladi
   - Database ga yoziladi
   - Celery Beat uchun periodic task yaratiladi

2. **Xabar Yuborish:**
   - Celery Beat belgilangan vaqtda task ni ishga tushiradi
   - Task mavjud `send_message_to_auto_folder` funksiyasini chaqiradi
   - Natija database ga yoziladi

3. **Error Handling:**
   - 3 marta retry qiladi
   - Error message database ga saqlanadi
   - Status yangilanadi

## Monitoring

### Celery Flower (ixtiyoriy):
```bash
pip install flower
celery -A backend flower
```
Keyin http://localhost:5555 ga boring.

## Production Deployment

Production muhitida Redis server kerak:
```bash
# Redis o'rnatish (Ubuntu/Debian)
sudo apt install redis-server

# Redis ishga tushirish
sudo systemctl start redis
sudo systemctl enable redis
```

Environment variables:
```bash
export REDIS_URL="redis://localhost:6379/0"
export DATABASE_URL="your_postgres_url"
```
