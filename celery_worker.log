[2025-07-26 05:48:57,230: WARNING/MainProcess] /Users/<USER>/apps/invest/taxi_userbot/venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-07-26 05:48:57,246: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-07-26 05:48:57,247: WARNING/MainProcess] /Users/<USER>/apps/invest/taxi_userbot/venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-07-26 05:48:57,252: INFO/MainProcess] mingle: searching for neighbors
